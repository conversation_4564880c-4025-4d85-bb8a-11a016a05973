import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';
import '../models/devis.dart';
import '../models/commande.dart';
import '../models/client.dart';
import '../core/services/logging_service.dart';
import 'pdf_service.dart';

class WhatsAppService {
  /// Envoie un devis par WhatsApp en format PDF
  static Future<bool> envoyerDevisPDF({
    required Devis devis,
    required Client client,
  }) async {
    try {
      LoggingService.info(
          'Génération du PDF pour le devis ${devis.numeroFormate}');

      // Générer le PDF
      final pdfFile = await PDFService.genererPDFDevis(
        devis: devis,
        client: client,
      );

      LoggingService.info('PDF généré: ${pdfFile.path}');

      // Partager le PDF via WhatsApp
      final result = await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text:
            'Devis ${devis.numeroFormate} - VitaBrosse Pro\n\nBonjour ${client.nomComplet},\n\nVeuillez trouver ci-joint votre devis.\n\nCordialement,\nL\'équipe VitaBrosse Pro',
        subject: 'Devis ${devis.numeroFormate}',
      );

      LoggingService.info('Partage PDF terminé: ${result.status}');
      return result.status == ShareResultStatus.success;
    } catch (e) {
      LoggingService.error('Erreur lors de l\'envoi du devis PDF', error: e);
      return false;
    }
  }

  /// Envoie une commande par WhatsApp en format PDF
  static Future<bool> envoyerCommandePDF({
    required Commande commande,
    required Client client,
  }) async {
    try {
      LoggingService.info('Génération du PDF pour la commande ${commande.id}');

      // Générer le PDF
      final pdfFile = await PDFService.genererPDFCommande(
        commande: commande,
        client: client,
      );

      LoggingService.info('PDF généré: ${pdfFile.path}');

      // Partager le PDF via WhatsApp
      final result = await Share.shareXFiles(
        [XFile(pdfFile.path)],
        text:
            'Commande ${commande.id} - VitaBrosse Pro\n\nBonjour ${client.nomComplet},\n\nVeuillez trouver ci-joint votre commande.\n\nCordialement,\nL\'équipe VitaBrosse Pro',
        subject: 'Commande ${commande.id}',
      );

      LoggingService.info('Partage PDF terminé: ${result.status}');
      return result.status == ShareResultStatus.success;
    } catch (e) {
      LoggingService.error('Erreur lors de l\'envoi de la commande PDF',
          error: e);
      return false;
    }
  }

  /// Envoie un devis par WhatsApp (méthode texte - dépréciée)
  static Future<bool> envoyerDevis({
    required Devis devis,
    required Client client,
    String? messagePersonnalise,
  }) async {
    try {
      final message = genererMessageDevis(devis, client, messagePersonnalise);
      final phoneNumber = _nettoyerNumeroTelephone(client.primaryPhone);

      if (phoneNumber.isEmpty) {
        throw Exception('Numéro de téléphone invalide');
      }

      final url = _construireUrlWhatsApp(phoneNumber, message);
      return await _lancerWhatsApp(url);
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'envoi du devis par WhatsApp',
        error: e,
      );
      return false;
    }
  }

  /// Envoie une commande par WhatsApp
  static Future<bool> envoyerCommande({
    required Commande commande,
    required Client client,
    String? messagePersonnalise,
  }) async {
    try {
      final message = genererMessageCommande(
        commande,
        client,
        messagePersonnalise,
      );
      final phoneNumber = _nettoyerNumeroTelephone(client.primaryPhone);

      if (phoneNumber.isEmpty) {
        throw Exception('Numéro de téléphone invalide');
      }

      final url = _construireUrlWhatsApp(phoneNumber, message);
      return await _lancerWhatsApp(url);
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'envoi de la commande par WhatsApp',
        error: e,
      );
      return false;
    }
  }

  /// Génère le message pour un devis
  static String genererMessageDevis(
    Devis devis,
    Client client,
    String? messagePersonnalise,
  ) {
    final buffer = StringBuffer();

    // Salutation
    buffer.writeln('Bonjour ${client.nomComplet},');
    buffer.writeln();

    // Message personnalisé ou message par défaut
    if (messagePersonnalise?.isNotEmpty == true) {
      buffer.writeln(messagePersonnalise);
    } else {
      buffer.writeln('Voici votre devis :');
    }
    buffer.writeln();

    // Détails du devis
    buffer.writeln('📋 *${devis.numeroFormate}*');
    buffer.writeln('📅 Date : ${_formaterDate(devis.dateCreation)}');
    buffer.writeln(
      '⏰ Valable jusqu\'au : ${_formaterDate(devis.dateExpiration)}',
    );
    buffer.writeln();

    // Articles
    buffer.writeln('📦 *Articles :*');
    for (final item in devis.items) {
      buffer.writeln('• ${item.designation}');
      buffer.writeln(
        '  Qté: ${item.quantite} ${item.unite} × ${item.prixUnitaireFormate} = ${item.sousTotalFormate}',
      );
    }
    buffer.writeln();

    // Totaux
    buffer.writeln('💰 *Récapitulatif :*');
    buffer.writeln('Sous-total HT : ${devis.sousTotalFormate}');
    if (devis.montantRemise > 0) {
      buffer.writeln('Remise : -${devis.montantRemiseFormate}');
      buffer.writeln('Total HT : ${devis.totalHTFormate}');
    }
    buffer.writeln('TVA (${devis.tauxTva}%) : ${devis.montantTvaFormate}');
    buffer.writeln('*Total TTC : ${devis.totalTTCFormate}*');
    buffer.writeln();

    // Conditions
    buffer.writeln('📄 *Conditions :*');
    buffer.writeln(devis.conditionsValidite);
    buffer.writeln();

    if (devis.notes?.isNotEmpty == true) {
      buffer.writeln('📝 *Notes :*');
      buffer.writeln(devis.notes);
      buffer.writeln();
    }

    // Signature
    buffer.writeln('Cordialement,');
    if (devis.contactCommercial?.isNotEmpty == true) {
      buffer.writeln(devis.contactCommercial);
    } else {
      buffer.writeln('Votre équipe commerciale');
    }

    return buffer.toString();
  }

  /// Génère le message pour une commande
  static String genererMessageCommande(
    Commande commande,
    Client client,
    String? messagePersonnalise,
  ) {
    final buffer = StringBuffer();

    // Salutation
    buffer.writeln('Bonjour ${client.nomComplet},');
    buffer.writeln();

    // Message personnalisé ou message par défaut
    if (messagePersonnalise?.isNotEmpty == true) {
      buffer.writeln(messagePersonnalise);
    } else {
      buffer.writeln('Confirmation de votre commande :');
    }
    buffer.writeln();

    // Détails de la commande
    buffer.writeln('🛒 *Commande #${commande.id}*');
    buffer.writeln('📅 Date : ${_formaterDate(commande.dateCommande)}');
    buffer.writeln('📊 Statut : ${_formaterStatutCommande(commande.statut)}');
    buffer.writeln();

    // Articles
    if (commande.items.isNotEmpty) {
      buffer.writeln('📦 *Articles commandés :*');
      for (final item in commande.items) {
        buffer.writeln('• ${item.nomProduit}');
        buffer.writeln(
          '  Qté: ${item.quantite} × ${item.prixUnitaire.toStringAsFixed(3)}DT = ${item.sousTotal.toStringAsFixed(3)}DT',
        );
      }
      buffer.writeln();
    }

    // Total
    buffer.writeln(
      '💰 *Total : ${commande.montantTotal.toStringAsFixed(3)}DT*',
    );
    buffer.writeln();

    if (commande.notes?.isNotEmpty == true) {
      buffer.writeln('📝 *Notes :*');
      buffer.writeln(commande.notes);
      buffer.writeln();
    }

    // Signature
    buffer.writeln('Merci pour votre confiance !');
    buffer.writeln('Votre équipe commerciale');

    return buffer.toString();
  }

  /// Nettoie et formate le numéro de téléphone
  static String _nettoyerNumeroTelephone(String telephone) {
    if (telephone.isEmpty) return '';

    // Supprimer tous les caractères non numériques sauf le +
    String numero = telephone.replaceAll(RegExp(r'[^\d+]'), '');

    // Si le numéro commence par 0, le remplacer par +216 (Tunisie)
    if (numero.startsWith('0')) {
      numero = '+216${numero.substring(1)}';
    }

    // Si le numéro ne commence pas par +, ajouter +216
    if (!numero.startsWith('+')) {
      numero = '+216$numero';
    }

    return numero;
  }

  /// Construit l'URL WhatsApp
  static String _construireUrlWhatsApp(String phoneNumber, String message) {
    final encodedMessage = Uri.encodeComponent(message);
    // Remove + sign for WhatsApp URL (wa.me expects numbers without +)
    String cleanPhoneNumber =
        phoneNumber.startsWith('+') ? phoneNumber.substring(1) : phoneNumber;
    return 'https://wa.me/$cleanPhoneNumber?text=$encodedMessage';
  }

  /// Lance WhatsApp
  static Future<bool> _lancerWhatsApp(String url) async {
    final uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      return await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      throw Exception('Impossible d\'ouvrir WhatsApp');
    }
  }

  /// Formate une date pour l'affichage
  static String _formaterDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Formate le statut d'une commande
  static String _formaterStatutCommande(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }

  /// Vérifie si WhatsApp est disponible sur l'appareil
  static Future<bool> estWhatsAppDisponible() async {
    try {
      final uri = Uri.parse('https://wa.me/');
      return await canLaunchUrl(uri);
    } catch (e) {
      return false;
    }
  }
}
