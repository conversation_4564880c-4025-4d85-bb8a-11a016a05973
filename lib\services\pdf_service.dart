import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/commande.dart';
import '../models/devis.dart';
import '../models/client.dart';

class PDFService {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat =
      NumberFormat('#,##0.000', 'fr_FR');

  /// Génère un PDF pour une commande
  static Future<File> genererPDFCommande({
    required Commande commande,
    required Client client,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            _buildHeader('COMMANDE'),
            pw.SizedBox(height: 20),
            _buildCommandeInfo(commande, client),
            pw.SizedBox(height: 20),
            _buildItemsTable(commande.items),
            pw.SizedBox(height: 20),
            _buildCommandeTotal(commande),
            pw.Spacer(),
            _buildFooter(),
          ];
        },
      ),
    );

    return await _savePDF(pdf, 'commande_${commande.id}');
  }

  /// Génère un PDF pour un devis
  static Future<File> genererPDFDevis({
    required Devis devis,
    required Client client,
  }) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            _buildHeader('DEVIS'),
            pw.SizedBox(height: 20),
            _buildDevisInfo(devis, client),
            pw.SizedBox(height: 20),
            _buildItemsTable(devis.items),
            pw.SizedBox(height: 20),
            _buildDevisTotal(devis),
            pw.SizedBox(height: 20),
            _buildDevisConditions(devis),
            pw.Spacer(),
            _buildFooter(),
          ];
        },
      ),
    );

    return await _savePDF(pdf, 'devis_${devis.numeroFormate}');
  }

  /// Construit l'en-tête du document
  static pw.Widget _buildHeader(String type) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'VitaBrosse Pro',
              style: pw.TextStyle(
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.green700,
              ),
            ),
            pw.SizedBox(height: 4),
            pw.Text(
              'Solutions professionnelles',
              style: pw.TextStyle(
                fontSize: 12,
                color: PdfColors.grey600,
              ),
            ),
          ],
        ),
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: pw.BoxDecoration(
            color: PdfColors.green700,
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            type,
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.white,
            ),
          ),
        ),
      ],
    );
  }

  /// Construit les informations de la commande
  static pw.Widget _buildCommandeInfo(Commande commande, Client client) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'INFORMATIONS CLIENT',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(client.nomComplet,
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text(client.adresse, style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Tél: ${client.primaryPhone}',
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Email: ${client.email}',
                  style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
        ),
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'DÉTAILS COMMANDE',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text('N° Commande: ${commande.id}',
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Date: ${_dateFormat.format(commande.dateCommande)}',
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Statut: ${_getStatutText(commande.statut)}',
                  style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
        ),
      ],
    );
  }

  /// Construit les informations du devis
  static pw.Widget _buildDevisInfo(Devis devis, Client client) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'INFORMATIONS CLIENT',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(client.nomComplet,
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text(client.adresse, style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Tél: ${client.primaryPhone}',
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Email: ${client.email}',
                  style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
        ),
        pw.Expanded(
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'DÉTAILS DEVIS',
                style: pw.TextStyle(
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text('N° Devis: ${devis.numeroFormate}',
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text('Date: ${_dateFormat.format(devis.dateCreation)}',
                  style: const pw.TextStyle(fontSize: 12)),
              pw.Text(
                  'Valable jusqu\'au: ${_dateFormat.format(devis.dateExpiration)}',
                  style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
        ),
      ],
    );
  }

  /// Construit le tableau des articles
  static pw.Widget _buildItemsTable(List<dynamic> items) {
    // Check if any item has a discount to determine if we need the discount column
    final hasDiscounts =
        items.any((item) => item.remise != null && item.remise > 0);

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: hasDiscounts
          ? {
              0: const pw.FlexColumnWidth(3),
              1: const pw.FlexColumnWidth(1),
              2: const pw.FlexColumnWidth(1.5),
              3: const pw.FlexColumnWidth(1.2),
              4: const pw.FlexColumnWidth(1.5),
            }
          : {
              0: const pw.FlexColumnWidth(3),
              1: const pw.FlexColumnWidth(1),
              2: const pw.FlexColumnWidth(1.5),
              3: const pw.FlexColumnWidth(1.5),
            },
      children: [
        // En-tête
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: hasDiscounts
              ? [
                  _buildTableCell('ARTICLE', isHeader: true),
                  _buildTableCell('QTÉ', isHeader: true),
                  _buildTableCell('PRIX UNIT.', isHeader: true),
                  _buildTableCell('REMISE', isHeader: true),
                  _buildTableCell('TOTAL', isHeader: true),
                ]
              : [
                  _buildTableCell('ARTICLE', isHeader: true),
                  _buildTableCell('QTÉ', isHeader: true),
                  _buildTableCell('PRIX UNIT.', isHeader: true),
                  _buildTableCell('TOTAL', isHeader: true),
                ],
        ),
        // Articles
        ...items.map((item) {
          // Get the correct property names for different item types
          final nomProduit = item.nomProduit ?? item.designation ?? 'Article';
          final prixUnitaire = item.prixUnitaire ?? item.prixUnitaireHT ?? 0.0;
          final quantite = item.quantite ?? 0;

          // Calculate totals based on item type
          double sousTotal;
          if (item.runtimeType.toString().contains('DevisItem')) {
            // For DevisItem, use sousTotalApresRemise if available
            sousTotal = item.sousTotalApresRemise ?? (prixUnitaire * quantite);
          } else {
            // For CommandeItem, use sousTotal
            sousTotal = item.sousTotal ?? (prixUnitaire * quantite);
          }

          // Calculate discount info
          String discountText = '-';
          if (item.remise != null && item.remise > 0) {
            if (item.remiseEnPourcentage == true) {
              discountText = '${item.remise}%';
            } else {
              discountText = '${_currencyFormat.format(item.remise)} DT';
            }
          }

          return pw.TableRow(
            children: hasDiscounts
                ? [
                    _buildTableCell(nomProduit),
                    _buildTableCell('$quantite'),
                    _buildTableCell(
                        '${_currencyFormat.format(prixUnitaire)} DT'),
                    _buildTableCell(discountText),
                    _buildTableCell('${_currencyFormat.format(sousTotal)} DT'),
                  ]
                : [
                    _buildTableCell(nomProduit),
                    _buildTableCell('$quantite'),
                    _buildTableCell(
                        '${_currencyFormat.format(prixUnitaire)} DT'),
                    _buildTableCell('${_currencyFormat.format(sousTotal)} DT'),
                  ],
          );
        }),
      ],
    );
  }

  /// Construit une cellule de tableau
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 11,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  /// Construit le total de la commande
  static pw.Widget _buildCommandeTotal(Commande commande) {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 280,
        padding: const pw.EdgeInsets.all(16),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(4),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Sous-total brut
            pw.Text(
              'Sous-total brut: ${_currencyFormat.format(commande.sousTotal)} DT',
              style: const pw.TextStyle(fontSize: 12),
            ),
            pw.SizedBox(height: 4),

            // Remise articles (si applicable)
            if (commande.totalRemiseArticles > 0) ...[
              pw.Text(
                'Remise articles: -${_currencyFormat.format(commande.totalRemiseArticles)} DT',
                style: pw.TextStyle(fontSize: 12, color: PdfColors.red600),
              ),
              pw.SizedBox(height: 4),
            ],

            // Sous-total après remise articles
            pw.Text(
              'Sous-total: ${_currencyFormat.format(commande.sousTotalApresRemiseArticles)} DT',
              style: const pw.TextStyle(fontSize: 12),
            ),
            pw.SizedBox(height: 4),

            // Remise globale (si applicable)
            if (commande.montantRemiseGlobale > 0) ...[
              pw.Text(
                'Remise globale ${commande.remisePourcentage > 0 ? "(${commande.remisePourcentage}%)" : ""}: -${_currencyFormat.format(commande.montantRemiseGlobale)} DT',
                style: pw.TextStyle(fontSize: 12, color: PdfColors.red600),
              ),
              pw.SizedBox(height: 4),
            ],

            // Total HT
            pw.Text(
              'Total HT: ${_currencyFormat.format(commande.totalHT)} DT',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 4),

            // TVA
            pw.Text(
              'TVA (${commande.tauxTva}%): ${_currencyFormat.format(commande.montantTva)} DT',
              style: const pw.TextStyle(fontSize: 12),
            ),

            pw.Divider(color: PdfColors.grey300),

            // Total TTC
            pw.Text(
              'TOTAL TTC: ${_currencyFormat.format(commande.totalTTC)} DT',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.green700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Construit le total du devis
  static pw.Widget _buildDevisTotal(Devis devis) {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 280,
        padding: const pw.EdgeInsets.all(16),
        decoration: pw.BoxDecoration(
          border: pw.Border.all(color: PdfColors.grey300),
          borderRadius: pw.BorderRadius.circular(4),
        ),
        child: pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Sous-total brut
            pw.Text(
              'Sous-total brut: ${_currencyFormat.format(devis.sousTotal)} DT',
              style: const pw.TextStyle(fontSize: 12),
            ),
            pw.SizedBox(height: 4),

            // Sous-total après remise articles (si différent du brut)
            if (devis.sousTotalApresRemiseArticles != devis.sousTotal) ...[
              pw.Text(
                'Après remise articles: ${_currencyFormat.format(devis.sousTotalApresRemiseArticles)} DT',
                style: const pw.TextStyle(fontSize: 12),
              ),
              pw.SizedBox(height: 4),
            ],

            // Remise globale (si applicable)
            if (devis.montantRemise > 0) ...[
              pw.Text(
                'Remise ${devis.remisePourcentage > 0 ? "(${devis.remisePourcentage}%)" : ""}: -${_currencyFormat.format(devis.montantRemise)} DT',
                style: pw.TextStyle(fontSize: 12, color: PdfColors.red600),
              ),
              pw.SizedBox(height: 4),
            ],

            // Total HT
            pw.Text(
              'Total HT: ${_currencyFormat.format(devis.totalHT)} DT',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
            pw.SizedBox(height: 4),

            // TVA
            pw.Text(
              'TVA (${devis.tauxTva}%): ${_currencyFormat.format(devis.montantTva)} DT',
              style: const pw.TextStyle(fontSize: 12),
            ),

            pw.Divider(color: PdfColors.grey300),

            // Total TTC
            pw.Text(
              'TOTAL TTC: ${_currencyFormat.format(devis.totalTTC)} DT',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.green700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Construit les conditions du devis
  static pw.Widget _buildDevisConditions(Devis devis) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'CONDITIONS',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.grey800,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          devis.conditionsValidite.isNotEmpty
              ? devis.conditionsValidite
              : 'Conditions générales de vente applicables.',
          style: const pw.TextStyle(fontSize: 11),
        ),
        if (devis.notes?.isNotEmpty == true) ...[
          pw.SizedBox(height: 12),
          pw.Text(
            'NOTES',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.grey800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            devis.notes!,
            style: const pw.TextStyle(fontSize: 11),
          ),
        ],
      ],
    );
  }

  /// Construit le pied de page
  static pw.Widget _buildFooter() {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 16),
      decoration: const pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300)),
      ),
      child: pw.Center(
        child: pw.Text(
          'VitaBrosse Pro - Solutions professionnelles pour votre entreprise',
          style: pw.TextStyle(
            fontSize: 10,
            color: PdfColors.grey600,
          ),
        ),
      ),
    );
  }

  /// Sauvegarde le PDF
  static Future<File> _savePDF(pw.Document pdf, String filename) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$filename.pdf');

    // Créer le dossier s'il n'existe pas
    await file.parent.create(recursive: true);

    final bytes = await pdf.save();
    await file.writeAsBytes(bytes);

    return file;
  }

  /// Convertit le statut de commande en texte
  static String _getStatutText(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }
}
