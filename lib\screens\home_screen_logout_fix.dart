// This is a temporary file to fix the logout button implementation
// The main issue is that the old async code is still present after changing onPressed

// Here's the corrected logout button implementation:

Container(
  width: double.infinity,
  margin: const EdgeInsets.only(bottom: 24),
  child: Consumer<AuthProvider>(
    builder: (context, authProvider, _) {
      return ElevatedButton.icon(
        onPressed: () => _performLogout(context, authProvider),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFEF4444),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        icon: const Icon(Icons.logout_rounded, size: 20),
        label: const Text(
          'Déconnexion',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    },
  ),
),

// The _performLogout method should be added to the _HomeScreenState class:

/// Robust logout method that handles all edge cases
Future<void> _performLogout(BuildContext context, AuthProvider authProvider) async {
  print('🔓 Logout button pressed - Starting robust logout process');
  
  // Prevent multiple logout attempts
  if (authProvider.isLoading) {
    print('🔄 Logout already in progress, ignoring');
    return;
  }

  // Store context references early to avoid issues
  final navigator = Navigator.of(context);
  final scaffoldMessenger = ScaffoldMessenger.of(context);
  bool dialogShown = false;
  
  try {
    // Close drawer first if it's open
    if (Scaffold.of(context).isDrawerOpen) {
      navigator.pop();
      await Future.delayed(const Duration(milliseconds: 100));
    }

    // Verify context is still valid
    if (!mounted || !context.mounted) {
      print('❌ Context no longer valid, aborting logout');
      return;
    }

    print('🚀 Starting logout process...');
    
    // Show loading dialog with better error handling
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) => PopScope(
          canPop: false, // Prevent back button
          child: const Dialog(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 20),
                  Text('Déconnexion en cours...'),
                ],
              ),
            ),
          ),
        ),
      );
      dialogShown = true;
      print('✅ Loading dialog shown');
    } catch (dialogError) {
      print('❌ Failed to show loading dialog: $dialogError');
    }

    // Small delay to ensure dialog is rendered
    await Future.delayed(const Duration(milliseconds: 200));

    // Perform the actual logout
    print('🔄 Calling authProvider.logout()...');
    await authProvider.logout();
    print('✅ authProvider.logout() completed successfully');

    // Close loading dialog if it was shown
    if (dialogShown && mounted && context.mounted) {
      try {
        Navigator.of(context, rootNavigator: true).pop();
        print('✅ Loading dialog closed');
      } catch (popError) {
        print('❌ Error closing loading dialog: $popError');
      }
    }

    // Add delay to ensure logout is fully processed
    await Future.delayed(const Duration(milliseconds: 300));

    print('✅ Logout completed successfully - AuthWrapper will handle navigation');

  } catch (e) {
    print('❌ Error during logout process: $e');

    // Ensure loading dialog is closed
    if (dialogShown && mounted && context.mounted) {
      try {
        Navigator.of(context, rootNavigator: true).pop();
      } catch (popError) {
        print('❌ Error closing dialog after error: $popError');
      }
    }

    // Show error message if context is still valid
    if (mounted && context.mounted) {
      try {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la déconnexion: $e'),
            backgroundColor: const Color(0xFFEF4444),
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Réessayer',
              textColor: Colors.white,
              onPressed: () => _performLogout(context, authProvider),
            ),
          ),
        );
      } catch (snackBarError) {
        print('❌ Error showing error snackbar: $snackBarError');
      }
    }

    // Force logout as last resort
    try {
      print('🔄 Attempting force logout as fallback...');
      // Try logout again as a fallback
      await authProvider.logout();
    } catch (forceError) {
      print('❌ Force logout also failed: $forceError');
    }
  }
}
