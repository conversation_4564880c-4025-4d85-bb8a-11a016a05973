import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';
import '../../providers/rapport_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/mission.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../merchandising/calendrier_missions_screen.dart';
import '../merchandising/missions_du_jour_screen.dart';
import '../merchandising/mes_rapports_screen.dart';
import '../merchandising/creer_rapport_screen.dart';

class MerchandiserHomeScreen extends StatefulWidget {
  const MerchandiserHomeScreen({super.key});

  @override
  State<MerchandiserHomeScreen> createState() => _MerchandiserHomeScreenState();
}

class _MerchandiserHomeScreenState extends State<MerchandiserHomeScreen> {
  int _selectedIndex = 0;
  bool _isLoadingData = false;
  final GlobalKey<_MerchandiserDashboardTabState> _dashboardKey = GlobalKey();
  Timer? _refreshTimer;

  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      MerchandiserDashboardTab(key: _dashboardKey),
      const MissionsDuJourScreen(),
      CalendrierMissionsScreen(),
      const MesRapportsScreen(),
    ];

    // Différer le chargement des données après la construction du widget
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshData();
      _startPeriodicRefresh();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startPeriodicRefresh() {
    // Refresh data every 5 minutes when on dashboard
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      if (_selectedIndex == 0 && mounted) {
        _refreshData();
      }
    });
  }

  Future<void> _refreshData() async {
    if (_isLoadingData) return; // Prevent multiple simultaneous loads

    _isLoadingData = true;

    try {
      final missionProvider = Provider.of<MissionProvider>(
        context,
        listen: false,
      );
      final rapportProvider = Provider.of<RapportProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Get the real merchandiser ID from auth provider
      final merchandiserId = authProvider.currentUserId;
      if (merchandiserId == null) return;

      await Future.wait([
        // Charger les missions du jour
        missionProvider.chargerMissionsDuJour(merchandiserId),
        missionProvider.chargerMissionsEnRetard(merchandiserId),
        // Charger les rapports
        rapportProvider.chargerRapportsParMerchandiser(merchandiserId),
        rapportProvider.chargerRapportsBrouillon(merchandiserId),
      ]);
    } catch (e) {
      // Handle errors silently - providers manage their own error states
    } finally {
      _isLoadingData = false;
    }
  }

  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: const Color(0xFFF8FAFC),
      child: SafeArea(
        child: Column(
          children: [
            // Profile Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
                ),
              ),
              child: Consumer<AuthProvider>(
                builder: (context, authProvider, _) {
                  final userEmail = authProvider.currentUser ?? '';
                  final userName = userEmail.isNotEmpty
                      ? userEmail.split('@')[0].replaceAll('.', ' ')
                      : 'Merchandiser';

                  return Column(
                    children: [
                      // Logo
                      const VitaBrosseLogo(height: 40, showText: false),
                      const SizedBox(height: 16),

                      // User Avatar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            userName.isNotEmpty
                                ? userName[0].toUpperCase()
                                : 'M',
                            style: const TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF10B981),
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // User Info
                      Text(
                        userName.toUpperCase(),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF1E293B),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        userEmail,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF64748B),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'MERCHANDISER',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF10B981),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            const SizedBox(height: 24),

            // Menu Items
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Settings
                    Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: ListTile(
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF10B981,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.settings_outlined,
                            color: Color(0xFF10B981),
                            size: 20,
                          ),
                        ),
                        title: const Text(
                          'Paramètres',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1E293B),
                          ),
                        ),
                        trailing: const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Color(0xFF64748B),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to settings
                        },
                      ),
                    ),

                    const Spacer(),

                    // Logout Button
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 24),
                      child: Consumer<AuthProvider>(
                        builder: (context, authProvider, _) {
                          return ElevatedButton.icon(
                            onPressed: () async {
                              print('🔓 Merchandiser logout button pressed');

                              // Prevent multiple logout attempts
                              if (authProvider.isLoading) {
                                print(
                                    '🔄 Logout already in progress, ignoring');
                                return;
                              }

                              // Close drawer first
                              if (context.mounted) {
                                Navigator.pop(context);
                              }

                              // Get references for error handling
                              if (!context.mounted) return;
                              final scaffoldMessenger =
                                  ScaffoldMessenger.of(context);

                              // Start logout process directly without confirmation
                              print(
                                  '🚀 Starting direct merchandiser logout process...');
                              try {
                                // Show loading indicator
                                showDialog(
                                  context: context,
                                  barrierDismissible: false,
                                  builder: (BuildContext context) =>
                                      const Dialog(
                                    child: Padding(
                                      padding: EdgeInsets.all(20),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          CircularProgressIndicator(),
                                          SizedBox(width: 20),
                                          Text('Déconnexion...'),
                                        ],
                                      ),
                                    ),
                                  ),
                                );

                                // Close loading dialog BEFORE logout to prevent persistence
                                if (context.mounted) {
                                  Navigator.of(context, rootNavigator: true)
                                      .pop();
                                }

                                // Small delay to ensure dialog is closed
                                await Future.delayed(
                                    const Duration(milliseconds: 100));

                                // Perform logout
                                print('🔄 Calling authProvider.logout()...');
                                await authProvider.logout();
                                print('✅ authProvider.logout() completed');

                                // Add small delay to ensure dialog is closed and logout is complete
                                await Future.delayed(
                                    const Duration(milliseconds: 500));

                                // Don't navigate manually - let AuthWrapper handle it automatically
                                print(
                                    '✅ Logout completed, AuthWrapper will handle navigation');
                              } catch (e) {
                                print('❌ Error during logout: $e');

                                // Ensure loading dialog is closed
                                try {
                                  if (context.mounted) {
                                    Navigator.of(context, rootNavigator: true)
                                        .pop();
                                  }
                                } catch (popError) {
                                  print('❌ Error closing dialog: $popError');
                                }

                                // Check if context is still mounted before showing error
                                if (!context.mounted) {
                                  print(
                                      '❌ Context no longer mounted, cannot show error');
                                  return;
                                }

                                // Show error message
                                print('🔄 Showing error message...');
                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Erreur lors de la déconnexion: $e',
                                    ),
                                    backgroundColor: const Color(0xFFEF4444),
                                    duration: const Duration(seconds: 3),
                                  ),
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFEF4444),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            icon: const Icon(Icons.logout_rounded, size: 20),
                            label: const Text(
                              'Déconnexion',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: _buildDrawer(),
      body: _screens[_selectedIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          selectedItemColor: const Color(0xFF10B981),
          unselectedItemColor: const Color(0xFF64748B),
          backgroundColor: Colors.white,
          elevation: 0,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
            // Refresh data when returning to dashboard
            if (index == 0) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _refreshData();
                // Also refresh the dashboard tab if it's available
                _dashboardKey.currentState?._refreshData();
              });
            }
          },
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.dashboard_outlined),
              activeIcon: Icon(Icons.dashboard),
              label: 'Tableau de bord',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.today_outlined),
              activeIcon: Icon(Icons.today),
              label: 'Missions',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.calendar_month_outlined),
              activeIcon: Icon(Icons.calendar_month),
              label: 'Calendrier',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.description_outlined),
              activeIcon: Icon(Icons.description),
              label: 'Rapports',
            ),
          ],
        ),
      ),
    );
  }
}

// Separate Dashboard Tab Widget
class MerchandiserDashboardTab extends StatefulWidget {
  const MerchandiserDashboardTab({super.key});

  @override
  State<MerchandiserDashboardTab> createState() =>
      _MerchandiserDashboardTabState();
}

class _MerchandiserDashboardTabState extends State<MerchandiserDashboardTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isLoading = false;

  Future<void> _refreshData() async {
    if (_isLoading) return; // Prevent multiple simultaneous loads

    setState(() {
      _isLoading = true;
    });

    try {
      final missionProvider = Provider.of<MissionProvider>(
        context,
        listen: false,
      );
      final rapportProvider = Provider.of<RapportProvider>(
        context,
        listen: false,
      );
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Get the real merchandiser ID from auth provider
      final merchandiserId = authProvider.currentUserId;
      if (merchandiserId == null) {
        return;
      }

      await Future.wait([
        // Charger les missions du jour
        missionProvider.chargerMissionsDuJour(merchandiserId),
        missionProvider.chargerMissionsEnRetard(merchandiserId),
        // Charger les rapports
        rapportProvider.chargerRapportsParMerchandiser(merchandiserId),
        rapportProvider.chargerRapportsBrouillon(merchandiserId),
      ]);
    } catch (e) {
      // Silently handle errors - providers will handle their own error states
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshData,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight:
                  MediaQuery.of(context).size.width < 600 ? 120 : 140,
              floating: false,
              pinned: true,
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
              elevation: 0,
              scrolledUnderElevation: 2,
              leading: Builder(
                builder: (context) => IconButton(
                  icon: const Icon(
                    Icons.menu,
                    color: Color(0xFF1E293B),
                    size: 28,
                  ),
                  tooltip: 'Menu',
                  onPressed: () => Scaffold.of(context).openDrawer(),
                ),
              ),
              actions: [
                if (_isLoading)
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
                      ),
                    ),
                  )
                else
                  IconButton(
                    icon: const Icon(
                      Icons.refresh,
                      color: Color(0xFF1E293B),
                    ),
                    tooltip: 'Actualiser',
                    onPressed: _refreshData,
                  ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                titlePadding: const EdgeInsets.only(left: 20, bottom: 16),
                title: Row(
                  children: [
                    VitaBrosseLogo(
                      height: MediaQuery.of(context).size.width < 600 ? 24 : 28,
                      showText: false,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'VitaBrosse®',
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              color: const Color(0xFF1F2937),
                              fontSize: MediaQuery.of(context).size.width < 600
                                  ? 18
                                  : 20,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Text(
                            'Merchandising Pro',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey.shade600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white,
                        const Color(0xFF10B981).withValues(alpha: 0.05),
                        const Color(0xFF059669).withValues(alpha: 0.05),
                      ],
                      stops: const [0.0, 0.7, 1.0],
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(child: _buildDashboard()),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboard() {
    return Consumer3<MissionProvider, RapportProvider, AuthProvider>(
      builder: (
        context,
        missionProvider,
        rapportProvider,
        authProvider,
        child,
      ) {
        // Show loading indicator if data is being loaded
        if (_isLoading &&
            missionProvider.missionsDuJour.isEmpty &&
            missionProvider.missionsEnRetard.isEmpty &&
            rapportProvider.rapportsMerchandising.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(50.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Chargement des données...',
                    style: TextStyle(
                      color: Color(0xFF64748B),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Show error message if there are errors
        if (missionProvider.error != null || rapportProvider.error != null) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Color(0xFFEF4444),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Erreur de chargement',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    missionProvider.error ?? rapportProvider.error ?? '',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Color(0xFF64748B),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      missionProvider.effacerErreur();
                      rapportProvider.effacerErreur();
                      _refreshData();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Réessayer'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF10B981),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final missionsDuJour = missionProvider.missionsDuJour;
        final missionsEnRetard = missionProvider.missionsEnRetard;
        final rapportsMerchandising = rapportProvider.rapportsMerchandising;
        final rapportsBrouillon = rapportsMerchandising
            .where((r) => r.statut == 'brouillon')
            .toList();
        final userEmail = authProvider.currentUser ?? '';
        final userName = userEmail.isNotEmpty
            ? userEmail.split('@')[0].replaceAll('.', ' ')
            : 'Merchandiser';

        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Color(0xFF10B981), Color(0xFF059669)],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF10B981).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.store,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Bonjour ${userName.toUpperCase()}!',
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Prêt pour une nouvelle journée de merchandising?',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Statistics Cards
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Missions du jour',
                      missionsDuJour.length.toString(),
                      Icons.today,
                      const Color(0xFF10B981),
                      () {
                        // Navigate to missions tab
                        if (context.findAncestorStateOfType<
                                _MerchandiserHomeScreenState>() !=
                            null) {
                          context
                              .findAncestorStateOfType<
                                  _MerchandiserHomeScreenState>()!
                              .setState(() {
                            context
                                .findAncestorStateOfType<
                                    _MerchandiserHomeScreenState>()!
                                ._selectedIndex = 1;
                          });
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'En retard',
                      missionsEnRetard.length.toString(),
                      Icons.schedule,
                      const Color(0xFFEF4444),
                      () {
                        // Navigate to missions tab
                        if (context.findAncestorStateOfType<
                                _MerchandiserHomeScreenState>() !=
                            null) {
                          context
                              .findAncestorStateOfType<
                                  _MerchandiserHomeScreenState>()!
                              .setState(() {
                            context
                                .findAncestorStateOfType<
                                    _MerchandiserHomeScreenState>()!
                                ._selectedIndex = 1;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'Rapports brouillon',
                      rapportsBrouillon.length.toString(),
                      Icons.edit_note,
                      const Color(0xFFF59E0B),
                      () {
                        // Navigate to reports tab
                        if (context.findAncestorStateOfType<
                                _MerchandiserHomeScreenState>() !=
                            null) {
                          context
                              .findAncestorStateOfType<
                                  _MerchandiserHomeScreenState>()!
                              .setState(() {
                            context
                                .findAncestorStateOfType<
                                    _MerchandiserHomeScreenState>()!
                                ._selectedIndex = 3;
                          });
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'Rapports envoyés',
                      rapportsMerchandising
                          .where((r) => r.statut == 'envoye')
                          .length
                          .toString(),
                      Icons.send,
                      const Color(0xFF3B82F6),
                      () {
                        // Navigate to reports tab
                        if (context.findAncestorStateOfType<
                                _MerchandiserHomeScreenState>() !=
                            null) {
                          context
                              .findAncestorStateOfType<
                                  _MerchandiserHomeScreenState>()!
                              .setState(() {
                            context
                                .findAncestorStateOfType<
                                    _MerchandiserHomeScreenState>()!
                                ._selectedIndex = 3;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Missions urgentes
              if (missionsEnRetard.isNotEmpty) ...[
                const Text(
                  'Missions en retard',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFFEF4444),
                  ),
                ),
                const SizedBox(height: 16),
                ...missionsEnRetard
                    .take(3)
                    .map((mission) => _buildMissionCard(mission, true)),
                const SizedBox(height: 24),
              ],

              // Missions du jour
              if (missionsDuJour.isNotEmpty) ...[
                const Text(
                  'Missions du jour',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF1E293B),
                  ),
                ),
                const SizedBox(height: 16),
                ...missionsDuJour
                    .take(3)
                    .map((mission) => _buildMissionCard(mission, false)),
                const SizedBox(height: 16),
                if (missionsDuJour.length > 3)
                  Center(
                    child: TextButton.icon(
                      onPressed: () {
                        // Navigate to missions tab
                        if (context.findAncestorStateOfType<
                                _MerchandiserHomeScreenState>() !=
                            null) {
                          context
                              .findAncestorStateOfType<
                                  _MerchandiserHomeScreenState>()!
                              .setState(() {
                            context
                                .findAncestorStateOfType<
                                    _MerchandiserHomeScreenState>()!
                                ._selectedIndex = 1;
                          });
                        }
                      },
                      icon: const Icon(Icons.arrow_forward),
                      label: const Text('Voir toutes les missions du jour'),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF10B981),
                      ),
                    ),
                  ),
              ],

              const SizedBox(height: 32),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w700,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF64748B),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMissionCard(Mission mission, bool isUrgent) {
    return GestureDetector(
      onTap: () {
        // Navigate to mission details or create report
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CreerRapportScreen(missionId: mission.id),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: isUrgent
              ? Border.all(color: const Color(0xFFEF4444), width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isUrgent
                          ? const Color(0xFFEF4444).withValues(alpha: 0.1)
                          : const Color(0xFF10B981).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      mission.estTerminee
                          ? Icons.check_circle
                          : Icons.assignment,
                      color: isUrgent
                          ? const Color(0xFFEF4444)
                          : const Color(0xFF10B981),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          mission.titre,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: const Color(0xFF1E293B),
                            decoration: mission.estTerminee
                                ? TextDecoration.lineThrough
                                : null,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          mission.clientNom,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF64748B),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatutColor(mission.statut),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      mission.statutAffichage,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: _getStatutTextColor(mission.statut),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: isUrgent
                        ? const Color(0xFFEF4444)
                        : const Color(0xFF64748B),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${mission.dateEcheance.day}/${mission.dateEcheance.month}/${mission.dateEcheance.year}',
                    style: TextStyle(
                      color: isUrgent
                          ? const Color(0xFFEF4444)
                          : const Color(0xFF64748B),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return const Color(0xFFF59E0B).withValues(alpha: 0.1);
      case 'en_cours':
        return const Color(0xFF3B82F6).withValues(alpha: 0.1);
      case 'terminee':
        return const Color(0xFF10B981).withValues(alpha: 0.1);
      case 'annulee':
        return const Color(0xFFEF4444).withValues(alpha: 0.1);
      default:
        return const Color(0xFF64748B).withValues(alpha: 0.1);
    }
  }

  Color _getStatutTextColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return const Color(0xFFF59E0B);
      case 'en_cours':
        return const Color(0xFF3B82F6);
      case 'terminee':
        return const Color(0xFF10B981);
      case 'annulee':
        return const Color(0xFFEF4444);
      default:
        return const Color(0xFF64748B);
    }
  }
}
